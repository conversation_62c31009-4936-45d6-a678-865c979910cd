{"files": {"index.html": {"frameworks": ["<PERSON><PERSON><PERSON>", "pg.insight.events", "pg.svg.lib", "pg.fontawesome.5", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "pg.html", "pine.cone.lib", "pg.components"]}}, "open-pages": ["index.html", "szablon.html"], "urls": {"index.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 800}]}, "index2.html": {"open-with-wrapper": false}, "pages/about.html": {"open-with-wrapper": false}, "pages/blog.html": {"open-with-wrapper": false}, "pages/contact.html": {"open-with-wrapper": false}, "szablon.html": {"open-with-wrapper": false}}, "breakpoints": [], "frameworks": ["<PERSON><PERSON><PERSON>", "pg.insight.events", "pg.svg.lib", "pg.fontawesome.5", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "pg.html", "pine.cone.lib", "pg.components"]}