// Mapa stron do plików
const pageFiles = {
    'home-page': 'pages/home.html',
    'o-nas-page': 'pages/about.html',
    'uslugi-sprzatanie-po-zgonach-page': 'pages/services/cleaning-after-death.html',
    'uslugi-sprzatanie-po-pozarach-page': 'pages/services/cleaning-after-fire.html',
    'uslugi-sprzatanie-po-zalaniach-page': 'pages/services/cleaning-after-flood.html',
    'uslugi-ozonowanie-page': 'pages/services/ozonation.html',
    'uslugi-dezynfekcja-po-chorobach-zakaznych-page': 'pages/services/disinfection-after-diseases.html',
    'uslugi-usuwanie-krwi-plynow-ustrojowych-page': 'pages/services/blood-removal.html',
    'uslugi-dezynfekcja-po-osobach-starszych-zaniedbanych-page': 'pages/services/disinfection-elderly.html',
    'uslugi-sprzatanie-po-zmarłych-lokatorach-page': 'pages/services/cleaning-after-deceased-tenants.html',
    'uslugi-dezynfekcja-po-eksmisjach-patologiach-page': 'pages/services/disinfection-evictions.html',
    'uslugi-neutralizacja-zapachow-skazen-page': 'pages/services/odor-neutralization.html',
    'uslugi-sprzatanie-zaniedbanych-ekstremalnie-zanieczyszczonych-page': 'pages/services/cleaning-neglected.html',
    'uslugi-dezynsekcja-deratyzacja-page': 'pages/services/pest-control.html',
    'realizacje-page': 'pages/realizations.html',
    'opinie-page': 'pages/reviews.html',
    'faq-page': 'pages/faq.html',
    'blog-page': 'pages/blog.html',
    'kontakt-page': 'pages/contact.html',
    'polityka-prywatnosci-page': 'pages/privacy-policy.html'
};

// Funkcja do ładowania treści strony
async function loadPageContent(pageId) {
    const pageFile = pageFiles[pageId];
    if (!pageFile) {
        console.error('Nie znaleziono pliku dla strony:', pageId);
        return;
    }

    try {
        const response = await fetch(pageFile);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const content = await response.text();
        
        // Znajdź kontener na treść strony
        const pageContent = document.getElementById('page-content');
        if (pageContent) {
            pageContent.innerHTML = content;
        } else {
            console.error('Nie znaleziono elementu #page-content');
            return;
        }
        
        // Aktywuj nową stronę
        activatePage(pageId);
        
        // Przewiń na górę
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
    } catch (error) {
        console.error('Błąd ładowania strony:', error);
        // Fallback - pokaż stronę główną
        loadPageContent('home-page');
    }
}

// Funkcja aktywacji strony
function activatePage(pageId) {
    // Ukryj wszystkie strony
    const allPages = document.querySelectorAll('.page');
    allPages.forEach(page => {
        page.classList.remove('active');
        page.classList.add('hidden');
    });
    
    // Pokaż aktywną stronę
    const activePage = document.getElementById(pageId);
    if (activePage) {
        activePage.classList.remove('hidden');
        activePage.classList.add('active');
    }
    
    // Aktualizuj nawigację
    updateActiveNavLink(pageId);
}

// Funkcja do aktualizacji aktywnego linku w nawigacji
function updateActiveNavLink(activePageId) {
    const navLinks = document.querySelectorAll('.main-nav .nav-link');
    navLinks.forEach(link => {
        // Remove active class from all links
        link.classList.remove('active');
        // Add active class if the link's data-target matches the active page ID
        if (link.dataset.target === activePageId) {
            link.classList.add('active');
        }
    });

    // Handle the 'Usługi' button for mobile dropdown
    const mobileServicesButton = document.getElementById('mobile-services-button');
    const mobileServicesDropdown = document.getElementById('mobile-services-dropdown');
    const mobileServicesArrow = document.getElementById('mobile-services-arrow');

    if (mobileServicesDropdown) {
        const isServicePageActive = Array.from(document.querySelectorAll('.service-page')).some(page => page.classList.contains('active'));
        if (isServicePageActive) {
            if (mobileServicesButton) mobileServicesButton.classList.add('active');
        } else {
            if (mobileServicesButton) mobileServicesButton.classList.remove('active');
            if (mobileServicesDropdown) mobileServicesDropdown.classList.add('hidden'); // Close dropdown if navigating away
            if (mobileServicesArrow) {
                mobileServicesArrow.classList.remove('fa-chevron-up');
                mobileServicesArrow.classList.add('fa-chevron-down');
            }
        }
    }
}

// Modyfikacja istniejącej funkcji showPage
function showPage(pageId) {
    loadPageContent(pageId);
}

// Inicjalizacja - załaduj stronę główną
document.addEventListener('DOMContentLoaded', () => {
    // Sprawdź hash URL
    const initialHash = window.location.hash.substring(1);
    if (initialHash && pageFiles[initialHash]) {
        loadPageContent(initialHash);
    } else {
        loadPageContent('home-page');
    }
    
    // Nasłuchuj zmian hash
    window.addEventListener('hashchange', () => {
        const hash = window.location.hash.substring(1);
        if (pageFiles[hash]) {
            loadPageContent(hash);
        }
    });
});