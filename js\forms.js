// --- Form Submission Handling (Client-side simulation) ---
// General function for form submission handling
function handleFormSubmission(formId, statusElementId) {
    const form = document.getElementById(formId);
    const statusElement = document.getElementById(statusElementId);

    if (form && statusElement) {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            // Simulate form data collection (for demonstration)
            const formData = new FormData(form);
            const data = {};
            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }
            console.log(`Formularz "${formId}" wysłany:`, data);

            // Show success message
            statusElement.classList.remove('hidden');
            statusElement.classList.add('show');
            
            // Reset form after a short delay
            setTimeout(() => {
                form.reset();
                statusElement.classList.remove('show');
                statusElement.classList.add('hidden');
            }, 5000); // Hide message after 5 seconds
        });
    }
}

// Initialize form handlers for all forms
document.addEventListener('DOMContentLoaded', () => {
    handleFormSubmission('footerForm', 'footer-form-status');
    handleFormSubmission('service-contact-form', 'service-form-status');
    handleFormSubmission('contact-page-form', 'contact-form-status');
}); 