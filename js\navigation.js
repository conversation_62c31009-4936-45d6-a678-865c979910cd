// Event listener dla wszystkich linków nawigacyjnych
document.addEventListener('DOMContentLoaded', () => {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.dataset.target;
            if (targetId) {
                // Zmień hash URL
                window.location.hash = targetId;
                // Strona zostanie załadowana przez page-loader.js
            }
        });
    });
});

// --- Mobile Menu Specific Logic ---
const mobileMenuButton = document.getElementById('mobile-menu-button');
const mobileMenu = document.getElementById('mobile-menu');
const mobileServicesButton = document.getElementById('mobile-services-button');
const mobileServicesDropdown = document.getElementById('mobile-services-dropdown');
const mobileServicesArrow = document.getElementById('mobile-services-arrow');

if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });
}

if (mobileServicesButton && mobileServicesDropdown && mobileServicesArrow) {
    mobileServicesButton.addEventListener('click', (e) => {
        mobileServicesDropdown.classList.toggle('hidden');
        mobileServicesArrow.classList.toggle('fa-chevron-down');
        mobileServicesArrow.classList.toggle('fa-chevron-up');
    });
}

// --- Desktop Services Dropdown Logic (Improved) ---
document.addEventListener('DOMContentLoaded', () => {
    const servicesDropdownDesktop = document.getElementById('services-dropdown-desktop');
    if (servicesDropdownDesktop) {
        const dropdownButton = servicesDropdownDesktop.querySelector('button');
        const dropdownMenu = servicesDropdownDesktop.querySelector('.dropdown-menu');
        let timeoutId;

        // Mouse enter na całym kontenerze dropdown
        servicesDropdownDesktop.addEventListener('mouseenter', () => {
            clearTimeout(timeoutId);
            dropdownMenu.classList.add('show');
        });

        // Mouse leave na całym kontenerze dropdown
        servicesDropdownDesktop.addEventListener('mouseleave', () => {
            timeoutId = setTimeout(() => {
                dropdownMenu.classList.remove('show');
            }, 150); // Krótszy delay
        });

        // Dodatkowe zabezpieczenie - mouse enter na menu
        dropdownMenu.addEventListener('mouseenter', () => {
            clearTimeout(timeoutId);
        });

        // Mouse leave na menu
        dropdownMenu.addEventListener('mouseleave', () => {
            timeoutId = setTimeout(() => {
                dropdownMenu.classList.remove('show');
            }, 100);
        });
    }
});

// --- Sticky Header Logic ---
const mainHeader = document.getElementById('main-header');
const topBar = document.querySelector('.bg-brand-dark-blue.text-brand-text-light-on-dark');
let topBarHeight = topBar ? topBar.offsetHeight : 0;

window.addEventListener("scroll", function() {
    let currentScroll = window.pageYOffset || document.documentElement.scrollTop;
    if (mainHeader) {
        if (currentScroll > topBarHeight) {
            mainHeader.classList.add("sticky-header-active");
        } else {
            mainHeader.classList.remove("sticky-header-active");
        }
    }
}, false);

// --- FAQ Accordion Logic ---
document.addEventListener('DOMContentLoaded', () => {
    const faqButtons = document.querySelectorAll('.faq-question-button');

    faqButtons.forEach(button => {
        button.addEventListener('click', () => {
            const answer = button.nextElementSibling;
            const isOpen = button.classList.contains('open');

            // Close all other open FAQs
            faqButtons.forEach(otherButton => {
                if (otherButton !== button && otherButton.classList.contains('open')) {
                    otherButton.classList.remove('open');
                    otherButton.nextElementSibling.classList.remove('open');
                }
            });

            // Toggle current FAQ
            if (isOpen) {
                button.classList.remove('open');
                answer.classList.remove('open');
            } else {
                button.classList.add('open');
                answer.classList.add('open');
            }
        });
    });
});

// --- Footer Services Toggle Logic ---
document.addEventListener('DOMContentLoaded', () => {
    const footerServicesListContainer = document.getElementById('footer-services-list-container');
    const footerServicesToggle = document.getElementById('footer-services-toggle');
    const footerServicesList = footerServicesListContainer ? footerServicesListContainer.querySelector('ul') : null;
    const initialItemsToShow = 6; // Zmienione z 6 na 7

    if (footerServicesList && footerServicesToggle) {
        // Hide extra items initially
        Array.from(footerServicesList.children).forEach((item, index) => {
            if (index >= initialItemsToShow) {
                item.classList.add('hidden');
            }
        });

        // Add class to container to enable max-height transition
        footerServicesListContainer.classList.add('footer-services-collapsed');

        footerServicesToggle.addEventListener('click', () => {
            const isExpanded = footerServicesListContainer.classList.contains('expanded');

            if (isExpanded) {
                // Collapse: hide extra items and change button text
                Array.from(footerServicesList.children).forEach((item, index) => {
                    if (index >= initialItemsToShow) {
                        item.classList.add('hidden');
                    }
                });
                footerServicesListContainer.classList.remove('expanded');
                footerServicesToggle.querySelector('span').textContent = 'Rozwiń więcej';
                footerServicesToggle.querySelector('i').classList.remove('fa-chevron-up');
                footerServicesToggle.querySelector('i').classList.add('fa-chevron-down');
            } else {
                // Expand: show all items and change button text
                Array.from(footerServicesList.children).forEach(item => {
                    item.classList.remove('hidden');
                });
                footerServicesListContainer.classList.add('expanded');
                footerServicesToggle.querySelector('span').textContent = 'Zwiń';
                footerServicesToggle.querySelector('i').classList.remove('fa-chevron-down');
                footerServicesToggle.querySelector('i').classList.add('fa-chevron-up');
            }
        });

        // Hide the toggle button if there are not enough items to hide
        if (footerServicesList.children.length <= initialItemsToShow) {
            footerServicesToggle.classList.add('hidden');
        }
    }
});