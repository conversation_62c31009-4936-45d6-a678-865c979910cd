/* Custom CSS Variables for Brand Colors */
:root {
    --brand-dark-blue: #0A2144;
    --brand-accent-blue: #3A7DFF;
    --brand-accent-blue-darker: #2a62d9;
    --brand-text-dark: #1F2937;
    --brand-text-light-on-dark: #E5E7EB;
    --brand-text-medium: #4B5563;
    --brand-light-gray: #F8F9FA;
    --brand-section-bg-darker: #EDF2F7;
    --footer-bg: #111827;
    --footer-text: #9CA3AF;
    --footer-heading: #E5E7EB;
}

/* Base Body Styles */
body {
    font-family: 'Poppins', sans-serif;
    color: var(--brand-text-medium);
}

/* Navigation Active Link Styling */
.main-nav a.active {
    color: var(--brand-accent-blue);
    font-weight: 600;
}

/* Dropdown Menu */
.dropdown-menu {
    display: none;
}
.dropdown-menu.show {
    display: block;
}

/* <PERSON>y Header Logic */
.sticky-header-active {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    background-color: white;
}

/* Hero Section Backgrounds */
.hero-section-bg {
    background-image: linear-gradient(rgba(10, 33, 68, 0.75), rgba(10, 33, 68, 0.75)), url('https://placehold.co/1920x1080/1a202c/ffffff?text=Solvictus+Tło+Hero');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.hero-subpage-bg {
    background-image: linear-gradient(rgba(10, 33, 68, 0.75), rgba(10, 33, 68, 0.75)), url('https://placehold.co/1920x400/0A2144/FFFFFF?text=Tło+Podstrony');
    background-size: cover;
    background-position: center;
}

/* Service Tile Background and Hover Effects */
.service-tile-bg {
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}
.service-tile-bg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(10, 33, 68, 0.5);
    transition: background-color 0.3s ease-in-out;
}
.service-tile-bg:hover::before {
    background-color: rgba(10, 33, 68, 0.7);
}
.service-tile-content {
    position: relative;
    z-index: 1;
}

/* Call to Action Section Background */
.cta-section-bg {
    background-image: linear-gradient(rgba(10, 33, 68, 0.85), rgba(10, 33, 68, 0.85)), url('https://placehold.co/1920x600/0A2144/FFFFFF?text=CTA+Tło');
    background-size: cover;
    background-position: center;
}

/* Attributes List Item with Icon Placeholder */
.attributes-list-item {
    padding-left: 2.5rem;
    position: relative;
    margin-bottom: 1rem;
}
.attributes-list-item .icon-placeholder {
    position: absolute;
    left: 0;
    top: 0.125rem;
    color: var(--brand-accent-blue);
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

/* Cennik Section Specific Styles */
.cennik-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease-in-out, background-color 0.3s ease-in-out;
    min-height: 280px;
}
.cennik-card:hover {
    box-shadow: 0 10px 15px rgba(0,0,0,0.15);
    background-color: var(--brand-section-bg-darker);
}
.cennik-card .price-display {
    transition: transform 0.3s ease-in-out, color 0.3s ease-in-out;
    display: inline-block;
    font-size: 2.25rem;
    line-height: 2.5rem;
}
.cennik-card:hover .price-display {
    transform: scale(1.15);
    color: var(--brand-accent-blue-darker);
}

/* Footer Link Hover Effects */
.footer-links a:hover {
    color: var(--brand-accent-blue);
    text-decoration: underline;
}

/* Footer Form Styling */
.footer-form input[type="text"],
.footer-form input[type="email"],
.footer-form input[type="number"],
.footer-form textarea {
    background-color: #374151;
    border-color: #4B5563;
    color: var(--brand-text-light-on-dark);
}
.footer-form input[type="text"]::placeholder,
.footer-form input[type="email"]::placeholder,
.footer-form input[type="number"]::placeholder,
.footer-form textarea::placeholder {
    color: #9CA3AF;
}
.footer-form button {
    background-color: var(--brand-accent-blue);
}
.footer-form button:hover {
    background-color: var(--brand-accent-blue-darker);
}
.footer-form .checkbox-label {
    color: var(--footer-text);
}
.footer-form .checkbox-label a {
    color: var(--brand-accent-blue);
    text-decoration: underline;
}
.footer-form .checkbox-label a:hover {
    color: var(--brand-accent-blue-darker);
}

/* Logo Loading Spinner */
.logo-loading-spinner {
    border: 3px solid rgba(58, 125, 255, 0.3);
    border-top: 3px solid var(--brand-accent-blue);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    display: inline-block;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Page Management */
.page {
    display: none;
}
.page.active {
    display: block;
}

/* Contact Form Status Message */
.form-status {
    display: none;
}
.form-status.show {
    display: block;
}

/* FAQ Accordion Styles */
.faq-item {
    border-bottom: 1px solid #E5E7EB;
}
.faq-question-button {
    width: 100%;
    text-align: left;
    padding: 1rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--brand-text-dark);
    transition: color 0.2s ease-in-out;
}
.faq-question-button:hover {
    color: var(--brand-accent-blue);
}
.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
    padding: 0 1rem;
    color: var(--brand-text-medium);
}
.faq-answer.open {
    max-height: 200px;
    padding-bottom: 1rem;
}
.faq-question-button i {
    transition: transform 0.3s ease-in-out;
}
.faq-question-button.open i {
    transform: rotate(180deg);
}

/* Footer Services Toggle */
.footer-services-list-container {
    max-height: 220px; /* Zwiększone z 175px na 220px dla długich nazw */
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}
.footer-services-list-container.expanded {
    max-height: 500px;
}
.footer-services-toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.5rem;
    margin-top: 1rem;
    background-color: var(--brand-dark-blue);
    color: var(--brand-accent-blue);
    border: 1px solid var(--brand-accent-blue);
    border-radius: 0.375rem;
    font-weight: 600;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}
.footer-services-toggle-button:hover {
    background-color: var(--brand-accent-blue);
    color: white;
    border-color: var(--brand-accent-blue);
}
.footer-services-toggle-button i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease-in-out;
}
.footer-services-toggle-button.expanded i {
    transform: rotate(180deg);
}

/* Certificates Section Styling */
.certificate-item {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}
.certificate-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0,0,0,0.15);
}
.certificate-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}
.certificate-item .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(10, 33, 68, 0.7);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}
.certificate-item:hover .overlay {
    opacity: 1;
}
.certificate-item .overlay i {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}
.certificate-item .overlay span {
    font-weight: 600;
    text-align: center;
}

/* Custom animation for Cennik button */
.cennik-button-animation:hover {
    animation: pulse-button 1s infinite;
}

@keyframes pulse-button {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(58, 125, 255, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(58, 125, 255, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(58, 125, 255, 0);
    }
}