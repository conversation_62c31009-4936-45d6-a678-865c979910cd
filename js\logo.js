// --- Logo Generation Function ---
async function generateAndPlaceLogo() {
    const headerLogoPlaceholder = document.getElementById('header-logo-placeholder');
    const headerLogoImg = document.getElementById('header-logo-img');
    const footerLogoPlaceholder = document.getElementById('footer-logo-placeholder');
    const footerLogoImg = document.getElementById('footer-logo-img');

    const prompt = "Logo design for 'SOLVICTUS', a company specializing in professional, discreet cleaning and restoration services for difficult situations (biohazard, post-fire, post-flood) and ozonation. The logo should be modern, clean, professional, trustworthy, and empathetic. Incorporate a stylized, abstract phoenix icon symbolizing rebirth, renewal, and overcoming adversity. Color palette: primary dark blue (#0A2144), accent bright blue (#3A7DFF), with white or light gray for text or details. The name 'SOLVICTUS' should be in a clear, modern sans-serif font (similar to Poppins, uppercase). Vector style, suitable for a website header. The phoenix should be elegant and subtle, not overly dominant or aggressive. Avoid overly complex, cluttered, or grim imagery.";

    try {
        // IMPORTANT: For a production application, the API Key should NEVER be directly in frontend code.
        // It should be used from a backend server which then proxies the image to the frontend.
        const payload = { instances: [{ prompt: prompt }], parameters: { "sampleCount": 1 } };
        const apiKey = ""; // API Key will be automatically added by the Canvas environment
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=${apiKey}`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorBody = await response.text();
            console.error(`HTTP Error generating logo: ${response.status}. Response: ${errorBody}`);
            if (headerLogoPlaceholder) headerLogoPlaceholder.textContent = 'SOLVICTUS'; // Fallback to text logo
            if (footerLogoPlaceholder) footerLogoPlaceholder.textContent = 'SOLVICTUS';
            return;
        }

        const result = await response.json();

        if (result.predictions && result.predictions.length > 0 && result.predictions[0].bytesBase64Encoded) {
            const imageUrl = `data:image/png;base64,${result.predictions[0].bytesBase64Encoded}`;
            
            if (headerLogoImg && headerLogoPlaceholder) {
                headerLogoImg.src = imageUrl;
                headerLogoImg.style.display = 'block';
                headerLogoPlaceholder.style.display = 'none';
            }
            if (footerLogoImg && footerLogoPlaceholder) {
                footerLogoImg.src = imageUrl;
                footerLogoImg.style.display = 'block';
                footerLogoPlaceholder.style.display = 'none';
            }
        } else {
            console.error('No logo image received in response:', result);
            if (headerLogoPlaceholder) headerLogoPlaceholder.textContent = 'SOLVICTUS';
            if (footerLogoPlaceholder) footerLogoPlaceholder.textContent = 'SOLVICTUS';
        }
    } catch (error) {
        console.error('Error during logo generation:', error);
        if (headerLogoPlaceholder) headerLogoPlaceholder.textContent = 'SOLVICTUS';
        if (footerLogoPlaceholder) footerLogoPlaceholder.textContent = 'SOLVICTUS';
    }
}

// Call logo generation function after page load
window.addEventListener('load', generateAndPlaceLogo); 